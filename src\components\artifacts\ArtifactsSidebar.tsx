import React, { useState, useRef, useCallback, useEffect } from 'react'
import { useAppStore } from '../../store'
import { ArtifactViewer } from './ArtifactViewer'
import { DocumentListing } from './controls/DocumentListing'

export function ArtifactsSidebar() {
  const {
    artifacts: { isOpen, isFullscreen, currentArtifact, artifacts, sidebarWidth },
    closeArtifacts,
    toggleArtifactsFullscreen,
    setArtifactsSidebarWidth
  } = useAppStore()

  const [isResizing, setIsResizing] = useState(false)
  const sidebarRef = useRef<HTMLDivElement>(null)
  const startXRef = useRef<number>(0)
  const startWidthRef = useRef<number>(0)

  // Keyboard shortcuts for fullscreen mode
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      // Escape key to exit fullscreen or close sidebar
      if (e.key === 'Escape') {
        if (isFullscreen) {
          toggleArtifactsFullscreen()
        } else {
          closeArtifacts()
        }
        e.preventDefault()
      }

      // F11 or F key to toggle fullscreen
      if ((e.key === 'F11' || (e.key === 'f' && (e.ctrlKey || e.metaKey))) && currentArtifact) {
        toggleArtifactsFullscreen()
        e.preventDefault()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, isFullscreen, currentArtifact, toggleArtifactsFullscreen, closeArtifacts])

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    startXRef.current = e.clientX
    startWidthRef.current = sidebarWidth
    
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    e.preventDefault()
  }, [sidebarWidth])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return
    
    const deltaX = startXRef.current - e.clientX
    const newWidth = Math.max(300, Math.min(800, startWidthRef.current + deltaX))
    setArtifactsSidebarWidth(newWidth)
  }, [isResizing, setArtifactsSidebarWidth])

  const handleMouseUp = useCallback(() => {
    setIsResizing(false)
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }, [handleMouseMove])

  // Don't render if not open
  if (!isOpen) return null

  const sidebarClasses = `
    fixed top-0 right-0 h-full bg-gray-800 border-l border-tertiary
    flex flex-col transition-all duration-300 ease-in-out z-40 shadow-2xl
    ${isFullscreen ? 'left-0 w-full bg-gray-900' : `w-[${sidebarWidth}px] max-w-[90vw] min-w-[300px]`}
    ${isResizing ? 'select-none' : ''}
    md:max-w-none
  `

  return (
    <>
      {/* Backdrop for fullscreen mode */}
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black/50 z-30"
          onClick={closeArtifacts}
        />
      )}
      
      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={sidebarClasses}
        style={{ width: isFullscreen ? '100%' : `${sidebarWidth}px` }}
        role="complementary"
        aria-label="Artifacts sidebar"
        aria-hidden={!isOpen}
      >
        {/* Resize handle - only show when not fullscreen */}
        {!isFullscreen && (
          <div
            className="absolute left-0 top-0 w-1 h-full cursor-col-resize hover:bg-primary/80 transition-all duration-200 group"
            onMouseDown={handleMouseDown}
          >
            <div className="absolute left-1 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-supplement1/60 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        )}

        {/* Header with minimal controls */}
        <div className="flex-shrink-0 border-b border-tertiary p-2 flex justify-end space-x-2">
          <button
            onClick={toggleArtifactsFullscreen}
            className="p-1.5 hover:bg-gray-700 rounded transition-colors"
            title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
          >
            <i className={`fa-solid ${isFullscreen ? 'fa-compress' : 'fa-expand'} text-gray-400`}></i>
          </button>
          <button
            onClick={closeArtifacts}
            className="p-1.5 hover:bg-gray-700 rounded transition-colors"
            title="Close artifacts"
          >
            <i className="fa-solid fa-xmark text-gray-400"></i>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col md:flex-row">
          {/* Document Listing - Show in collapsed mode, or minimal in fullscreen if multiple artifacts */}
          {!isFullscreen ? (
            <div className="w-full md:w-80 border-b md:border-b-0 md:border-r border-tertiary flex flex-col max-h-60 md:max-h-none">
              <DocumentListing />
            </div>
          ) : artifacts.length > 1 ? (
            <div className="w-64 border-r border-tertiary flex flex-col bg-gray-800/50">
              <div className="p-3 border-b border-tertiary">
                <h3 className="text-sm font-medium text-supplement1">Documents</h3>
              </div>
              <DocumentListing className="p-2" />
            </div>
          ) : null}

          {/* Artifact Viewer */}
          <div className="flex-1 overflow-hidden">
            {currentArtifact ? (
              <ArtifactViewer artifact={currentArtifact} />
            ) : (
              <div className="flex items-center justify-center h-full text-neutral-400">
                <div className="text-center">
                  <div className="text-4xl mb-4">🎨</div>
                  <div className="text-lg font-medium mb-2">No Artifact Selected</div>
                  <div className="text-sm">
                    {artifacts.length > 0
                      ? 'Select an artifact from the list'
                      : 'Artifacts will appear here when generated'
                    }
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

// Hook for managing sidebar state
export function useArtifactsSidebar() {
  const { 
    artifacts: { isOpen, isFullscreen, currentArtifact, artifacts },
    openArtifact,
    closeArtifacts,
    toggleArtifactsFullscreen,
    setActiveArtifact
  } = useAppStore()

  const openWithArtifact = useCallback((artifactId: string) => {
    const artifact = artifacts.find(a => a.id === artifactId)
    if (artifact) {
      openArtifact(artifact)
    }
  }, [artifacts, openArtifact])

  const hasArtifacts = artifacts.length > 0
  const canToggleFullscreen = isOpen && currentArtifact !== null

  return {
    isOpen,
    isFullscreen,
    currentArtifact,
    artifacts,
    hasArtifacts,
    canToggleFullscreen,
    openWithArtifact,
    closeArtifacts,
    toggleFullscreen: toggleArtifactsFullscreen,
    setActiveArtifact
  }
}
