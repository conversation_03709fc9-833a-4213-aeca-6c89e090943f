import React from 'react'
import { useAppStore } from '../../../store'
import { actionIcons } from './FilterLabels'

interface DocumentListingProps {
  className?: string
}

export function DocumentListing({ className = '' }: DocumentListingProps) {
  const { 
    artifacts: { artifacts, currentArtifact },
    setCurrentArtifact
  } = useAppStore()

  const getArtifactIcon = (type: string) => {
    switch (type) {
      case 'code':
      case 'json':
      case 'html':
        return 'fa-solid fa-code'
      case 'markdown':
        return 'fa-brands fa-markdown'
      case 'image':
        return 'fa-solid fa-image'
      default:
        return 'fa-solid fa-file-text'
    }
  }

  const getArtifactColor = (type: string) => {
    switch (type) {
      case 'code':
      case 'json':
      case 'html':
        return 'text-primary'
      case 'markdown':
        return 'text-secondary'
      case 'image':
        return 'text-supplement2'
      default:
        return 'text-supplement2'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  const handleCopyContent = async (artifact: any) => {
    try {
      await navigator.clipboard.writeText(artifact.content)
      // TODO: Show toast notification
    } catch (err) {
      console.error('Failed to copy content:', err)
    }
  }

  const handleDownload = (artifact: any) => {
    try {
      const blob = new Blob([artifact.content], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Failed to download file:', err)
    }
  }

  if (artifacts.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full text-gray-400 ${className}`}>
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-gray-700/50 rounded-lg flex items-center justify-center mx-auto mb-4">
            <i className="fa-solid fa-file-text text-2xl text-gray-500"></i>
          </div>
          <div className="text-lg font-medium mb-2">No Artifacts Yet</div>
          <div className="text-sm text-gray-500">
            Artifacts will appear here when generated in your conversations
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex-1 overflow-y-auto p-2 md:p-4 ${className}`}>
      <div className="space-y-3">
        {artifacts.map((artifact) => (
          <div
            key={artifact.id}
            className={`
              bg-gray-700/50 rounded-lg p-4 cursor-pointer transition-all duration-200
              hover:bg-gray-700 hover:shadow-lg
              ${currentArtifact?.id === artifact.id ? 'ring-2 ring-primary bg-gray-700' : ''}
            `}
            onClick={() => setCurrentArtifact(artifact)}
          >
            {/* Artifact Header */}
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center ${getArtifactColor(artifact.type)}`}>
                <i className={`${getArtifactIcon(artifact.type)} text-sm`}></i>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-supplement1 truncate">{artifact.title}</h3>
                <div className="flex items-center gap-2 text-xs text-gray-400">
                  <span className="capitalize">{artifact.type}</span>
                  <span>•</span>
                  <span>{formatTimestamp(artifact.timestamp)}</span>
                </div>
              </div>
            </div>

            {/* Content Preview */}
            <div className="mb-3">
              <div className="bg-gray-900 rounded p-3 text-sm font-mono max-h-20 overflow-hidden">
                <div className="line-clamp-3 text-supplement2">
                  {artifact.content.substring(0, 150)}
                  {artifact.content.length > 150 && '...'}
                </div>
              </div>
            </div>

            {/* Action Icons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                {/* Render/View */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setCurrentArtifact(artifact)
                  }}
                  className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                  title="View"
                >
                  <i className="fa-solid fa-eye"></i>
                </button>

                {/* Source */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    // TODO: Show source view
                  }}
                  className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                  title="Source"
                >
                  <i className="fa-solid fa-code"></i>
                </button>

                {/* Copy */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleCopyContent(artifact)
                  }}
                  className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                  title="Copy"
                >
                  <i className="fa-solid fa-copy"></i>
                </button>

                {/* Download */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDownload(artifact)
                  }}
                  className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                  title="Download"
                >
                  <i className="fa-solid fa-download"></i>
                </button>
              </div>

              {/* File size or line count */}
              <div className="text-xs text-gray-500">
                {artifact.content.split('\n').length} lines
              </div>
            </div>

            {/* Chat Context (if available) */}
            {artifact.messageId && (
              <div className="mt-3 pt-3 border-t border-gray-600">
                <div className="flex items-center gap-2 text-xs text-gray-400">
                  <i className="fa-solid fa-message"></i>
                  <span>From conversation</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      // TODO: Navigate to message in chat
                    }}
                    className="text-primary hover:text-primary/80 underline"
                  >
                    View in chat
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
