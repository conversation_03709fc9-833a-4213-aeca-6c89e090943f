import React, { useState, useEffect } from 'react'
import { useNetworkStore } from '../stores/networkStore'

interface LocalModelSetupWizardProps {
  isOpen: boolean
  onClose: () => void
}

const LocalModelSetupWizard: React.FC<LocalModelSetupWizardProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedProvider, setSelectedProvider] = useState<'ollama' | 'lmstudio' | null>(null)
  const { checkLocalModels, localModelsAvailable, ollamaConnected, lmStudioConnected } = useNetworkStore()

  const steps = [
    {
      title: 'Welcome to Private Mode',
      content: 'privacy-intro'
    },
    {
      title: 'Choose Your Local AI Provider',
      content: 'provider-selection'
    },
    {
      title: 'Installation Guide',
      content: 'installation-guide'
    },
    {
      title: 'Setup Complete',
      content: 'completion'
    }
  ]

  useEffect(() => {
    if (isOpen) {
      // Check for existing local models when wizard opens
      checkLocalModels()
    }
  }, [isOpen, checkLocalModels])

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleProviderSelect = (provider: 'ollama' | 'lmstudio') => {
    setSelectedProvider(provider)
    handleNext()
  }

  const handleComplete = () => {
    checkLocalModels()
    onClose()
  }

  const renderPrivacyIntro = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-secondary/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <i className="fa-solid fa-shield-halved text-2xl text-secondary"></i>
        </div>
        <h3 className="text-xl font-semibold text-supplement1 mb-2">Your Privacy Matters</h3>
        <p className="text-gray-400">
          Private Mode keeps your conversations completely local and secure.
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-start gap-3">
          <i className="fa-solid fa-check text-primary mt-1"></i>
          <div>
            <h4 className="font-medium text-supplement1">100% Local Processing</h4>
            <p className="text-sm text-gray-400">All AI processing happens on your device</p>
          </div>
        </div>
        
        <div className="flex items-start gap-3">
          <i className="fa-solid fa-check text-primary mt-1"></i>
          <div>
            <h4 className="font-medium text-supplement1">No Data Sharing</h4>
            <p className="text-sm text-gray-400">Your conversations never leave your computer</p>
          </div>
        </div>
        
        <div className="flex items-start gap-3">
          <i className="fa-solid fa-check text-primary mt-1"></i>
          <div>
            <h4 className="font-medium text-supplement1">Offline Capable</h4>
            <p className="text-sm text-gray-400">Works without internet connection</p>
          </div>
        </div>
      </div>

      <div className="bg-tertiary/20 border border-tertiary rounded-lg p-4">
        <div className="flex items-start gap-3">
          <i className="fa-solid fa-info-circle text-primary mt-1"></i>
          <div>
            <h4 className="font-medium text-supplement1">Setup Required</h4>
            <p className="text-sm text-gray-400">
              To use Private Mode, you'll need to install a local AI provider like Ollama or LM Studio.
            </p>
          </div>
        </div>
      </div>
    </div>
  )

  const renderProviderSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-supplement1 mb-2">Choose Your AI Provider</h3>
        <p className="text-gray-400">
          Select the local AI provider you'd like to use:
        </p>
      </div>

      <div className="space-y-4">
        <button
          onClick={() => handleProviderSelect('ollama')}
          className="w-full p-6 border-2 border-gray-600 hover:border-primary rounded-lg transition-colors text-left group"
        >
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
              <i className="fa-solid fa-robot text-xl text-primary"></i>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-supplement1 mb-1">Ollama</h4>
              <p className="text-sm text-gray-400 mb-2">
                Easy-to-use local AI with a simple command-line interface
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>• Free & Open Source</span>
                <span>• Large Model Library</span>
                <span>• Active Community</span>
              </div>
              {ollamaConnected && (
                <div className="mt-2 flex items-center gap-2">
                  <i className="fa-solid fa-check text-green-400"></i>
                  <span className="text-sm text-green-400">Already Connected</span>
                </div>
              )}
            </div>
          </div>
        </button>

        <button
          onClick={() => handleProviderSelect('lmstudio')}
          className="w-full p-6 border-2 border-gray-600 hover:border-primary rounded-lg transition-colors text-left group"
        >
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
              <i className="fa-solid fa-desktop text-xl text-primary"></i>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-supplement1 mb-1">LM Studio</h4>
              <p className="text-sm text-gray-400 mb-2">
                User-friendly desktop app with a graphical interface
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>• GUI Interface</span>
                <span>• Model Management</span>
                <span>• Performance Monitoring</span>
              </div>
              {lmStudioConnected && (
                <div className="mt-2 flex items-center gap-2">
                  <i className="fa-solid fa-check text-green-400"></i>
                  <span className="text-sm text-green-400">Already Connected</span>
                </div>
              )}
            </div>
          </div>
        </button>
      </div>

      {(ollamaConnected || lmStudioConnected) && (
        <div className="text-center">
          <button
            onClick={() => setCurrentStep(3)} // Skip to completion
            className="text-primary hover:text-primary/80 underline"
          >
            I already have local models set up
          </button>
        </div>
      )}
    </div>
  )

  const renderInstallationGuide = () => {
    if (!selectedProvider) return null

    const isOllama = selectedProvider === 'ollama'

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-supplement1 mb-2">
            Install {isOllama ? 'Ollama' : 'LM Studio'}
          </h3>
          <p className="text-gray-400">
            Follow these steps to set up your local AI provider:
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-gray-900">
              1
            </div>
            <div>
              <h4 className="font-medium text-supplement1">Download & Install</h4>
              <p className="text-sm text-gray-400 mb-2">
                {isOllama 
                  ? 'Visit ollama.ai and download the installer for your operating system'
                  : 'Visit lmstudio.ai and download LM Studio for your platform'
                }
              </p>
              <a
                href={isOllama ? 'https://ollama.ai' : 'https://lmstudio.ai'}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-primary hover:text-primary/80 text-sm"
              >
                <i className="fa-solid fa-external-link"></i>
                Open {isOllama ? 'ollama.ai' : 'lmstudio.ai'}
              </a>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-gray-900">
              2
            </div>
            <div>
              <h4 className="font-medium text-supplement1">Install a Model</h4>
              <p className="text-sm text-gray-400 mb-2">
                {isOllama 
                  ? 'Run "ollama pull llama2" in your terminal to download a model'
                  : 'Use the LM Studio interface to browse and download a model'
                }
              </p>
              {isOllama && (
                <code className="bg-gray-800 px-2 py-1 rounded text-sm text-primary">
                  ollama pull llama2
                </code>
              )}
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-sm font-bold text-gray-900">
              3
            </div>
            <div>
              <h4 className="font-medium text-supplement1">Start the Service</h4>
              <p className="text-sm text-gray-400">
                {isOllama 
                  ? 'Ollama should start automatically. If not, run "ollama serve"'
                  : 'Start LM Studio and load your model in the Local Server tab'
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-tertiary/20 border border-tertiary rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="fa-solid fa-lightbulb text-secondary mt-1"></i>
            <div>
              <h4 className="font-medium text-supplement1">Need Help?</h4>
              <p className="text-sm text-gray-400">
                Check the official documentation for detailed installation guides and troubleshooting.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderCompletion = () => (
    <div className="space-y-6 text-center">
      <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto">
        <i className="fa-solid fa-check text-2xl text-green-400"></i>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold text-supplement1 mb-2">
          {localModelsAvailable ? 'Setup Complete!' : 'Almost There!'}
        </h3>
        <p className="text-gray-400">
          {localModelsAvailable 
            ? 'Your local AI models are ready to use in Private Mode.'
            : 'Complete the installation steps and your local models will be detected automatically.'
          }
        </p>
      </div>

      {localModelsAvailable && (
        <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            <i className="fa-solid fa-shield-halved text-green-400"></i>
            <span className="font-medium text-green-400">Private Mode Ready</span>
          </div>
          <p className="text-sm text-gray-300">
            You can now chat with complete privacy using your local AI models.
          </p>
        </div>
      )}
    </div>
  )

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-lg font-semibold text-supplement1">{steps[currentStep].title}</h2>
            <div className="flex items-center gap-2 mt-1">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index <= currentStep ? 'bg-primary' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <i className="fa-solid fa-xmark text-gray-400"></i>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {steps[currentStep].content === 'privacy-intro' && renderPrivacyIntro()}
          {steps[currentStep].content === 'provider-selection' && renderProviderSelection()}
          {steps[currentStep].content === 'installation-guide' && renderInstallationGuide()}
          {steps[currentStep].content === 'completion' && renderCompletion()}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700">
          <button
            onClick={handleBack}
            disabled={currentStep === 0}
            className="px-4 py-2 text-gray-400 hover:text-supplement1 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Back
          </button>
          
          <div className="flex items-center gap-3">
            {currentStep === steps.length - 1 ? (
              <button
                onClick={handleComplete}
                className="px-6 py-2 bg-primary hover:bg-primary/80 text-gray-900 font-medium rounded-lg transition-colors"
              >
                Get Started
              </button>
            ) : currentStep === 1 ? (
              // Provider selection step - no next button, selection handles navigation
              null
            ) : (
              <button
                onClick={handleNext}
                className="px-6 py-2 bg-primary hover:bg-primary/80 text-gray-900 font-medium rounded-lg transition-colors"
              >
                Next
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LocalModelSetupWizard
