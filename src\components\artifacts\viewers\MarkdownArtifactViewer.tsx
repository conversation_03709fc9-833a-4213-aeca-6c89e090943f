import React, { useState } from 'react'
import { Artifact } from '../../../types'

interface MarkdownArtifactViewerProps {
  artifact: Artifact
}

export function MarkdownArtifactViewer({ artifact }: MarkdownArtifactViewerProps) {
  const [viewMode, setViewMode] = useState<'rendered' | 'source'>('rendered')

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content)
      console.log('Markdown copied to clipboard')
    } catch (error) {
      console.error('Failed to copy markdown:', error)
    }
  }

  const handleDownload = () => {
    const blob = new Blob([artifact.content], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleExportHtml = () => {
    const htmlContent = convertMarkdownToHtml(artifact.content)
    const blob = new Blob([htmlContent], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="h-full flex flex-col bg-neutral-900">
      {/* Minimal view mode toggle only */}
      <div className="flex-shrink-0 flex items-center justify-between p-2 bg-neutral-800/30 border-b border-neutral-700/50">
        <div className="flex items-center space-x-3">
          {/* View mode toggle */}
          <div className="flex bg-neutral-700/50 rounded-lg p-1">
            <button
              onClick={() => setViewMode('rendered')}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                viewMode === 'rendered'
                  ? 'bg-primary text-gray-900'
                  : 'text-neutral-300 hover:text-white'
              }`}
            >
              Rendered
            </button>
            <button
              onClick={() => setViewMode('source')}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                viewMode === 'source'
                  ? 'bg-primary text-gray-900'
                  : 'text-neutral-300 hover:text-white'
              }`}
            >
              Source
            </button>
          </div>
        </div>

        <span className="text-xs text-neutral-500">
          {artifact.content.split('\n').length} lines
        </span>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'rendered' ? (
          <div className="p-6">
            <MarkdownRenderer content={artifact.content} />
          </div>
        ) : (
          <div className="h-full">
            <pre className="p-4 text-sm font-mono text-neutral-100 whitespace-pre-wrap h-full overflow-auto">
              {artifact.content}
            </pre>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-t border-neutral-700 text-xs text-neutral-400">
        <div>
          {artifact.content.length} characters • {artifact.content.split('\n').length} lines
        </div>
        <div>
          Markdown
        </div>
      </div>
    </div>
  )
}

// Simple markdown renderer component
interface MarkdownRendererProps {
  content: string
}

function MarkdownRenderer({ content }: MarkdownRendererProps) {
  const renderMarkdown = (text: string) => {
    // This is a basic markdown renderer - in a real app you'd use a library like react-markdown
    let html = text
    
    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-white mt-6 mb-3">$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-white mt-8 mb-4">$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-white mt-8 mb-6">$1</h1>')
    
    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-white">$1</strong>')
    html = html.replace(/\*(.*?)\*/g, '<em class="italic text-neutral-200">$1</em>')
    
    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-neutral-800 p-4 rounded-lg my-4 overflow-x-auto"><code class="text-sm font-mono text-neutral-100">$1</code></pre>')
    
    // Inline code
    html = html.replace(/`(.*?)`/g, '<code class="bg-neutral-800 px-2 py-1 rounded text-sm font-mono text-neutral-100">$1</code>')
    
    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-indigo-400 hover:text-indigo-300 underline" target="_blank" rel="noopener noreferrer">$1</a>')
    
    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li class="text-neutral-200 mb-1">$1</li>')
    html = html.replace(/^- (.*$)/gim, '<li class="text-neutral-200 mb-1">$1</li>')
    
    // Wrap lists
    html = html.replace(/(<li.*<\/li>)/gs, '<ul class="list-disc list-inside space-y-1 my-4 text-neutral-200">$1</ul>')
    
    // Paragraphs
    html = html.replace(/\n\n/g, '</p><p class="text-neutral-200 mb-4">')
    html = '<p class="text-neutral-200 mb-4">' + html + '</p>'
    
    // Clean up empty paragraphs
    html = html.replace(/<p class="text-neutral-200 mb-4"><\/p>/g, '')
    
    return html
  }

  return (
    <div 
      className="prose prose-invert max-w-none"
      dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
    />
  )
}

// Helper function to convert markdown to HTML for export
function convertMarkdownToHtml(markdown: string): string {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exported Document</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #333;
        }
        h1, h2, h3 { color: #2d3748; margin-top: 2rem; }
        code { background: #f7fafc; padding: 0.2rem 0.4rem; border-radius: 0.25rem; }
        pre { background: #f7fafc; padding: 1rem; border-radius: 0.5rem; overflow-x: auto; }
        blockquote { border-left: 4px solid #e2e8f0; padding-left: 1rem; margin: 1rem 0; }
    </style>
</head>
<body>
    ${markdown.replace(/\n/g, '<br>')}
</body>
</html>
  `
  return htmlContent
}
