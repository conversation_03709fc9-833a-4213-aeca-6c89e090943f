import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { X, Key, Sliders, Save } from './Icons'
import { openRouterService } from '../services/openrouter'

interface SettingsProps {
  isOpen: boolean
  onClose: () => void
}

const Settings: React.FC<SettingsProps> = ({ isOpen, onClose }) => {
  const { settings, updateSettings, setModels } = useAppStore()
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)

  useEffect(() => {
    setLocalSettings(settings)
  }, [settings])

  const handleSave = async () => {
    try {
      setIsLoading(true)

      // Update settings in store and save to electron
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }
      
      // If API key changed, test it and load models
      if (localSettings.openRouterApiKey && localSettings.openRouterApiKey !== settings.openRouterApiKey) {
        openRouterService.setApiKey(localSettings.openRouterApiKey)
        const models = await openRouterService.getModels()
        setModels(models)
        
        if (models.length > 0) {
          setTestResult('✅ API key is valid! Loaded ' + models.length + ' models.')
        } else {
          setTestResult('⚠️ API key seems valid but no models were loaded.')
        }
      }
      
      setTimeout(() => {
        onClose()
      }, 1000)
      
    } catch (error) {
      console.error('Failed to save settings:', error)
      setTestResult('❌ Failed to save settings: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      openRouterService.setApiKey(localSettings.openRouterApiKey)

      // Use the new validation method
      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        // If validation passes, load models
        const models = await openRouterService.getModels()

        if (models.length > 0) {
          setTestResult('✅ API key is valid! Found ' + models.length + ' models.')
          setModels(models)
        } else {
          setTestResult('⚠️ API key seems valid but no models were loaded.')
        }
      } else {
        setTestResult('❌ API key validation failed: ' + validation.error)
      }
    } catch (error) {
      setTestResult('❌ API key test failed: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-neutral-900 rounded-lg border border-neutral-800 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <h2 className="text-lg font-semibold">Settings</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-neutral-800 rounded"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* API Key Section */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium mb-2">
              <Key className="h-4 w-4" />
              OpenRouter API Key
            </label>
            <input
              type="password"
              value={localSettings.openRouterApiKey || ''}
              onChange={(e) => setLocalSettings({ ...localSettings, openRouterApiKey: e.target.value })}
              placeholder="sk-or-..."
              className="input-field w-full"
            />
            <div className="flex items-center gap-2 mt-2">
              <button
                onClick={handleTestApiKey}
                disabled={isLoading}
                className="btn-secondary text-xs"
              >
                Test API Key
              </button>
              <a
                href="https://openrouter.ai/keys"
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-indigo-400 hover:text-indigo-300"
              >
                Get API Key
              </a>
            </div>
            {testResult && (
              <p className="text-xs mt-2 text-neutral-400">{testResult}</p>
            )}
          </div>

          {/* Info about chat settings */}
          <div className="bg-neutral-800/50 border border-neutral-700 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Sliders className="h-4 w-4 text-indigo-400" />
              <h3 className="font-medium">Chat Configuration</h3>
            </div>
            <p className="text-sm text-neutral-400 mb-3">
              Model selection, system prompts, and advanced parameters can be configured using the settings gear icon next to the send button in any chat.
            </p>
            <div className="text-xs text-neutral-500">
              💡 This allows you to adjust settings contextually while chatting
            </div>
          </div>


        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-neutral-800">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="btn-primary flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default Settings
