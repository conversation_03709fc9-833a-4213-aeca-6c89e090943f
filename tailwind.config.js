/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // ChatLo Brand Colors
        primary: "#8AB0BB",      // Teal - Main brand color
        secondary: "#FF8383",    // Coral - Accent/heart color
        tertiary: "#1B3E68",     // Navy - Deep accent
        supplement1: "#D5D8E0",  // Light gray - Text/borders
        supplement2: "#89AFBA",  // Muted teal - Secondary elements

        // Extended palette for UI components
        'chatlo-teal': {
          50: '#f0f9fa',
          100: '#daf0f3',
          200: '#b8e1e7',
          300: '#8AB0BB', // Primary
          400: '#6b9aa8',
          500: '#4f7c8a',
          600: '#3d5f6b',
          700: '#2d464f',
          800: '#1e2f35',
          900: '#0f171a'
        },
        'chatlo-coral': {
          50: '#fff5f5',
          100: '#ffe3e3',
          200: '#ffc9c9',
          300: '#FF8383', // Secondary
          400: '#ff5555',
          500: '#e53e3e',
          600: '#c53030',
          700: '#9c2626',
          800: '#742a2a',
          900: '#4a1414'
        },
        'chatlo-navy': {
          50: '#f7f8fa',
          100: '#eef1f5',
          200: '#dde3eb',
          300: '#c4cdd9',
          400: '#a6b3c4',
          500: '#8694a8',
          600: '#6b7a8f',
          700: '#556275',
          800: '#3d4a5c',
          900: '#1B3E68'  // Tertiary
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.3s ease-out',
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(10px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        }
      },
      backdropBlur: {
        xs: '2px',
      }
    },
  },
  plugins: [],
}
