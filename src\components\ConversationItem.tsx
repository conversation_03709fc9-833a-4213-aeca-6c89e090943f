import React from 'react';
import { MessageSquare, Edit3, Trash2, Pin } from './Icons';
import { Conversation } from '../types';

interface ConversationItemProps {
  conversation: Conversation;
  currentConversationId: string | null;
  handleSelectConversation: (id: string) => void;
  handleEditStart: (id: string, title: string, e: React.MouseEvent) => void;
  handleDeleteConversation: (id: string, e: React.MouseEvent) => void;
  editingId: string | null;
  editTitle: string;
  handleEditSave: (id: string) => Promise<void>;
  handleEditCancel: () => void;
  setEditTitle: (title: string) => void;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  currentConversationId,
  handleSelectConversation,
  handleEditStart,
  handleDeleteConversation,
  editingId,
  editTitle,
  handleEditSave,
  handleEditCancel,
  setEditTitle,
}) => {
  // Format the updated date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div
      key={conversation.id}
      className={`
        u1-sidebar-item group relative px-3 py-2 mx-1 transition-all duration-200
        ${currentConversationId === conversation.id
          ? 'bg-primary/10 border-l-2 border-primary text-supplement1'
          : 'hover:bg-gray-700/50 text-gray-300 hover:text-supplement1'
        }
      `}
      onClick={() => handleSelectConversation(conversation.id)}
    >
      <MessageSquare className={`h-4 w-4 shrink-0 ${currentConversationId === conversation.id ? 'text-primary' : 'text-gray-400'}`} />

      <div className="flex-1 min-w-0">
        {editingId === conversation.id ? (
          <input
            type="text"
            value={editTitle}
            onChange={(e) => setEditTitle(e.target.value)}
            onBlur={() => handleEditSave(conversation.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleEditSave(conversation.id);
              if (e.key === 'Escape') handleEditCancel();
            }}
            className="w-full bg-transparent border-none outline-none text-sm font-medium"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <>
            <div className="text-sm font-medium truncate">{conversation.title}</div>
            <div className="text-xs text-gray-500 truncate mt-0.5">
              {formatDate(conversation.updated_at)} • Click to open
            </div>
          </>
        )}
      </div>

      {conversation.is_pinned === 1 && (
        <Pin className="h-3 w-3 text-primary shrink-0 ml-2" />
      )}

      {/* Action buttons */}
      <div className="opacity-0 group-hover:opacity-100 flex items-center gap-1 transition-opacity ml-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleEditStart(conversation.id, conversation.title, e);
          }}
          className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-supplement1"
          title="Edit conversation"
        >
          <Edit3 className="h-3 w-3" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteConversation(conversation.id, e);
          }}
          className="p-1 hover:bg-gray-600 rounded text-gray-400 hover:text-secondary"
          title="Delete conversation"
        >
          <Trash2 className="h-3 w-3" />
        </button>
      </div>
    </div>
  );
};

export default ConversationItem;
