import React from 'react'
import { FileText, Image, File, Eye, Download } from './Icons'
import { FileRecord } from '../types'

interface FileAttachmentDisplayProps {
  files: FileRecord[]
  mode?: 'compact' | 'full'
  showPreview?: boolean
}

const FileAttachmentDisplay: React.FC<FileAttachmentDisplayProps> = ({
  files,
  mode = 'full',
  showPreview = true
}) => {
  if (!files || files.length === 0) return null

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <Image className="h-4 w-4 text-blue-400" />
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-400" />
      case 'word':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'excel':
        return <FileText className="h-4 w-4 text-green-600" />
      case 'powerpoint':
        return <FileText className="h-4 w-4 text-orange-600" />
      case 'text':
      case 'markdown':
        return <FileText className="h-4 w-4 text-neutral-400" />
      default:
        return <File className="h-4 w-4 text-neutral-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const handleFileOpen = async (file: FileRecord) => {
    try {
      if (window.electronAPI?.files) {
        const exists = await window.electronAPI.files.fileExists(file.filepath)
        if (exists) {
          // Open file with system default application
          // This would need to be implemented in the main process
          console.log('Opening file:', file.filepath)
        } else {
          console.warn('File not found:', file.filepath)
        }
      }
    } catch (error) {
      console.error('Error opening file:', error)
    }
  }

  const handleFileDownload = async (file: FileRecord) => {
    try {
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showSaveDialog({
          title: 'Save File',
          defaultPath: file.filename,
          filters: [
            { name: 'All Files', extensions: ['*'] }
          ]
        })

        if (!result.canceled && result.filePath) {
          // Copy file to selected location
          const content = await window.electronAPI.files.getFileContent(file.filepath)
          if (content) {
            // This would need to be implemented to save the file
            console.log('Downloading file to:', result.filePath)
          }
        }
      }
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  if (mode === 'compact') {
    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {files.map((file) => (
          <div
            key={file.id}
            className="flex items-center gap-2 bg-neutral-800/50 border border-neutral-700 rounded-lg px-3 py-2 text-sm"
          >
            {getFileIcon(file.file_type)}
            <span className="text-neutral-300 truncate max-w-[150px]">
              {file.filename}
            </span>
            <span className="text-neutral-500 text-xs">
              {formatFileSize(file.file_size)}
            </span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="mt-3 space-y-2">
      {files.map((file) => (
        <div
          key={file.id}
          className="bg-neutral-800/30 border border-neutral-700/50 rounded-lg p-3"
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 flex-1 min-w-0">
              {getFileIcon(file.file_type)}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm text-neutral-200 truncate">
                  {file.filename}
                </h4>
                <div className="flex items-center gap-3 mt-1 text-xs text-neutral-500">
                  <span>{formatFileSize(file.file_size)}</span>
                  <span>•</span>
                  <span className="capitalize">{file.file_type}</span>
                  <span>•</span>
                  <span>{new Date(file.updated_at).toLocaleDateString()}</span>
                </div>
                
                {/* Show extracted content preview for text files */}
                {file.extracted_content && showPreview && (
                  <div className="mt-2 p-2 bg-neutral-900/50 rounded text-xs text-neutral-400 max-h-20 overflow-hidden">
                    <div className="line-clamp-3">
                      {file.extracted_content.substring(0, 200)}
                      {file.extracted_content.length > 200 && '...'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-1 ml-3">
              {showPreview && (
                <button
                  onClick={() => handleFileOpen(file)}
                  className="h-8 w-8 flex items-center justify-center rounded hover:bg-neutral-700 transition-colors"
                  title="Open file"
                >
                  <Eye className="h-4 w-4 text-neutral-400" />
                </button>
              )}
              <button
                onClick={() => handleFileDownload(file)}
                className="h-8 w-8 flex items-center justify-center rounded hover:bg-neutral-700 transition-colors"
                title="Download file"
              >
                <Download className="h-4 w-4 text-neutral-400" />
              </button>
            </div>
          </div>

          {/* Image preview for image files */}
          {file.file_type === 'image' && showPreview && (
            <div className="mt-3">
              <div className="relative max-w-sm">
                <img
                  src={`file://${file.filepath}`}
                  alt={file.filename}
                  className="rounded border border-neutral-700 max-h-48 object-cover"
                  onError={(e) => {
                    // Hide image if it fails to load
                    e.currentTarget.style.display = 'none'
                  }}
                />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default FileAttachmentDisplay
