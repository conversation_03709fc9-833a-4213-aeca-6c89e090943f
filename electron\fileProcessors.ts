import * as fs from 'fs'
import * as path from 'path'
import * as XLSX from 'xlsx'
import * as mime from 'mime-types'

// Lazy require for optional dependencies
let pdfParse: any
let mammoth: any
let sharp: any
let tesseract: any

// Initialize modules with lazy loading
const initModules = () => {
  if (!pdfParse) {
    try {
      pdfParse = require('pdf-parse')
    } catch (error) {
      console.warn('pdf-parse not available:', error)
      pdfParse = null
    }
  }
  if (!mammoth) {
    try {
      mammoth = require('mammoth')
    } catch (error) {
      console.warn('mammoth not available:', error)
      mammoth = null
    }
  }
  if (!sharp) {
    try {
      sharp = require('sharp')
    } catch (error) {
      console.warn('sharp not available:', error)
      sharp = null
    }
  }
  if (!tesseract) {
    try {
      tesseract = require('tesseract.js')
    } catch (error) {
      console.warn('tesseract.js not available:', error)
      tesseract = null
    }
  }
}

export interface ProcessedFileContent {
  text?: string
  metadata?: any
  error?: string
}

export class FileProcessorService {
  
  // Process PDF files
  async processPDF(filePath: string): Promise<ProcessedFileContent> {
    try {
      initModules()

      if (!pdfParse) {
        return {
          error: 'PDF processing not available - pdf-parse module not loaded'
        }
      }

      const dataBuffer = fs.readFileSync(filePath)
      const data = await pdfParse(dataBuffer)

      return {
        text: data.text,
        metadata: {
          pages: data.numpages,
          info: data.info,
          version: data.version
        }
      }
    } catch (error: any) {
      console.error('Error processing PDF:', error)
      return {
        error: `Failed to process PDF: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process Word documents (.docx)
  async processWord(filePath: string): Promise<ProcessedFileContent> {
    try {
      initModules()

      if (!mammoth) {
        return {
          error: 'Word processing not available - mammoth module not loaded'
        }
      }

      const result = await mammoth.extractRawText({ path: filePath })

      return {
        text: result.value,
        metadata: {
          messages: result.messages,
          hasImages: result.messages.some((msg: any) => msg.type === 'image')
        }
      }
    } catch (error: any) {
      console.error('Error processing Word document:', error)
      return {
        error: `Failed to process Word document: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process Excel files (.xlsx, .xls)
  async processExcel(filePath: string): Promise<ProcessedFileContent> {
    try {
      const workbook = XLSX.readFile(filePath)
      const sheetNames = workbook.SheetNames
      let allText = ''
      const sheetsData: any = {}

      for (const sheetName of sheetNames) {
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        // Convert to text
        const sheetText = jsonData
          .map((row: any) => (row as any[]).join('\t'))
          .join('\n')
        
        allText += `Sheet: ${sheetName}\n${sheetText}\n\n`
        sheetsData[sheetName] = jsonData
      }

      return {
        text: allText.trim(),
        metadata: {
          sheets: sheetNames,
          sheetsData: sheetsData,
          totalSheets: sheetNames.length
        }
      }
    } catch (error: any) {
      console.error('Error processing Excel file:', error)
      return {
        error: `Failed to process Excel file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process PowerPoint files (.pptx)
  async processPowerPoint(filePath: string): Promise<ProcessedFileContent> {
    try {
      // For PowerPoint, we'll use a simpler approach since there's no direct library
      // This is a placeholder - in a real implementation, you might use a library like 'officegen' or similar
      const stats = fs.statSync(filePath)
      
      return {
        text: `PowerPoint presentation: ${path.basename(filePath)}\nFile size: ${stats.size} bytes\nNote: PowerPoint content extraction requires additional processing.`,
        metadata: {
          fileSize: stats.size,
          lastModified: stats.mtime,
          note: 'PowerPoint content extraction not fully implemented'
        }
      }
    } catch (error: any) {
      console.error('Error processing PowerPoint file:', error)
      return {
        error: `Failed to process PowerPoint file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process text files
  async processText(filePath: string): Promise<ProcessedFileContent> {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const stats = fs.statSync(filePath)
      
      return {
        text: content,
        metadata: {
          encoding: 'utf8',
          lines: content.split('\n').length,
          characters: content.length,
          fileSize: stats.size,
          lastModified: stats.mtime
        }
      }
    } catch (error: any) {
      console.error('Error processing text file:', error)
      return {
        error: `Failed to process text file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process markdown files
  async processMarkdown(filePath: string): Promise<ProcessedFileContent> {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const stats = fs.statSync(filePath)
      
      // Extract headers for metadata
      const headers = content.match(/^#+\s+(.+)$/gm) || []
      
      return {
        text: content,
        metadata: {
          encoding: 'utf8',
          lines: content.split('\n').length,
          characters: content.length,
          headers: headers.map(h => h.replace(/^#+\s+/, '')),
          fileSize: stats.size,
          lastModified: stats.mtime
        }
      }
    } catch (error: any) {
      console.error('Error processing markdown file:', error)
      return {
        error: `Failed to process markdown file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process image files
  async processImage(filePath: string): Promise<ProcessedFileContent> {
    try {
      initModules()

      if (!sharp) {
        console.warn('Sharp not available, using basic image processing')
        // Fallback: basic image processing without sharp
        const stats = fs.statSync(filePath)
        const mimeType = mime.lookup(filePath) || 'unknown'

        return {
          text: `Image: ${path.basename(filePath)}\nFile size: ${stats.size} bytes\nMIME type: ${mimeType}`,
          metadata: {
            mimeType: mimeType,
            fileSize: stats.size,
            lastModified: stats.mtime,
            processed: 'basic'
          }
        }
      }

      const stats = fs.statSync(filePath)
      const mimeType = mime.lookup(filePath) || 'unknown'

      // Get image metadata using sharp
      const metadata = await sharp(filePath).metadata()

      // Perform OCR text extraction
      let extractedText = ''
      let ocrConfidence = 0

      if (tesseract) {
        try {
          console.log('Performing OCR on image:', path.basename(filePath))
          const { data } = await tesseract.recognize(filePath, 'eng', {
            logger: (m: any) => {
              if (m.status === 'recognizing text') {
                console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`)
              }
            }
          })
          extractedText = data.text.trim()
          ocrConfidence = data.confidence
          console.log(`OCR completed with confidence: ${ocrConfidence}%`)
        } catch (ocrError) {
          console.warn('OCR failed:', ocrError)
          extractedText = '[OCR failed]'
        }
      }

      // Generate comprehensive image description
      const imageDescription = this.generateImageDescription(metadata, extractedText, ocrConfidence, stats, filePath)

      return {
        text: imageDescription,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          channels: metadata.channels,
          density: metadata.density,
          hasAlpha: metadata.hasAlpha,
          mimeType: mimeType,
          fileSize: stats.size,
          lastModified: stats.mtime,
          extractedText: extractedText,
          ocrConfidence: ocrConfidence,
          hasText: extractedText.length > 0 && extractedText !== '[OCR failed]'
        }
      }
    } catch (error: any) {
      console.error('Error processing image file:', error)
      console.error('Image file path:', filePath)
      console.error('Sharp available:', !!sharp)
      return {
        error: `Failed to process image file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Main processing method that routes to appropriate processor
  async processFile(filePath: string, fileType: string): Promise<ProcessedFileContent> {
    try {
      switch (fileType) {
        case 'pdf':
          return await this.processPDF(filePath)
        case 'word':
          return await this.processWord(filePath)
        case 'excel':
          return await this.processExcel(filePath)
        case 'powerpoint':
          return await this.processPowerPoint(filePath)
        case 'text':
          return await this.processText(filePath)
        case 'markdown':
          return await this.processMarkdown(filePath)
        case 'image':
          return await this.processImage(filePath)
        default:
          return {
            error: `Unsupported file type: ${fileType}`
          }
      }
    } catch (error: any) {
      console.error('Error in processFile:', error)
      return {
        error: `Failed to process file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Batch process multiple files
  async processFiles(files: Array<{ filePath: string, fileType: string }>): Promise<Array<ProcessedFileContent & { filePath: string }>> {
    const results = []
    
    for (const file of files) {
      const result = await this.processFile(file.filePath, file.fileType)
      results.push({
        ...result,
        filePath: file.filePath
      })
    }
    
    return results
  }

  // Check if file type is supported
  isFileTypeSupported(fileType: string): boolean {
    const supportedTypes = ['pdf', 'word', 'excel', 'powerpoint', 'text', 'markdown', 'image']
    return supportedTypes.includes(fileType)
  }

  // Get supported file extensions
  getSupportedExtensions(): string[] {
    return [
      '.pdf',
      '.doc', '.docx',
      '.xls', '.xlsx',
      '.ppt', '.pptx',
      '.txt',
      '.md',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
    ]
  }

  // Generate comprehensive image description for AI context
  private generateImageDescription(metadata: any, extractedText: string, ocrConfidence: number, stats: any, filePath: string): string {
    const filename = path.basename(filePath)
    const fileSize = this.formatFileSize(stats.size)

    let description = `IMAGE ANALYSIS: ${filename}\n`
    description += `===========================================\n\n`

    // Basic image properties
    description += `📊 TECHNICAL DETAILS:\n`
    description += `• Dimensions: ${metadata.width} × ${metadata.height} pixels\n`
    description += `• Format: ${metadata.format?.toUpperCase()}\n`
    description += `• File Size: ${fileSize}\n`
    description += `• Color Channels: ${metadata.channels}\n`
    description += `• Has Transparency: ${metadata.hasAlpha ? 'Yes' : 'No'}\n`
    if (metadata.density) {
      description += `• Resolution: ${metadata.density} DPI\n`
    }
    description += `\n`

    // Image classification based on properties
    description += `🎨 IMAGE CHARACTERISTICS:\n`
    const aspectRatio = metadata.width / metadata.height
    if (aspectRatio > 1.5) {
      description += `• Layout: Landscape/Wide format\n`
    } else if (aspectRatio < 0.7) {
      description += `• Layout: Portrait/Tall format\n`
    } else {
      description += `• Layout: Square/Balanced format\n`
    }

    if (metadata.width > 1920 || metadata.height > 1080) {
      description += `• Quality: High resolution\n`
    } else if (metadata.width < 800 && metadata.height < 600) {
      description += `• Quality: Low resolution/Thumbnail\n`
    } else {
      description += `• Quality: Standard resolution\n`
    }

    // Determine likely image type based on filename and properties
    const lowerFilename = filename.toLowerCase()
    if (lowerFilename.includes('screenshot') || lowerFilename.includes('screen')) {
      description += `• Type: Screenshot\n`
    } else if (lowerFilename.includes('photo') || lowerFilename.includes('img')) {
      description += `• Type: Photograph\n`
    } else if (lowerFilename.includes('chart') || lowerFilename.includes('graph')) {
      description += `• Type: Chart/Graph\n`
    } else if (lowerFilename.includes('logo') || lowerFilename.includes('icon')) {
      description += `• Type: Logo/Icon\n`
    } else {
      description += `• Type: General image\n`
    }
    description += `\n`

    // OCR Results
    if (extractedText && extractedText !== '[OCR failed]') {
      description += `📝 EXTRACTED TEXT (OCR Confidence: ${ocrConfidence.toFixed(1)}%):\n`
      description += `${extractedText}\n\n`

      // Text analysis
      const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length
      description += `📊 TEXT ANALYSIS:\n`
      description += `• Word Count: ${wordCount}\n`
      description += `• Character Count: ${extractedText.length}\n`

      if (wordCount > 50) {
        description += `• Content Type: Document/Article\n`
      } else if (wordCount > 10) {
        description += `• Content Type: Caption/Description\n`
      } else if (wordCount > 0) {
        description += `• Content Type: Label/Title\n`
      }
      description += `\n`
    } else if (extractedText === '[OCR failed]') {
      description += `📝 TEXT EXTRACTION: Failed to extract text\n\n`
    } else {
      description += `📝 TEXT EXTRACTION: No readable text detected\n\n`
    }

    // Usage suggestions for AI
    description += `🤖 AI PROCESSING SUGGESTIONS:\n`
    description += `• This image can be analyzed for visual content\n`
    if (extractedText && extractedText !== '[OCR failed]') {
      description += `• Text content is available for search and analysis\n`
      description += `• Can be used for document processing workflows\n`
    }
    if (metadata.width > 1000 && metadata.height > 1000) {
      description += `• High resolution suitable for detailed analysis\n`
    }
    description += `• Can be referenced in conversations about visual content\n`

    return description
  }

  // Helper method to format file sizes
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }
}
