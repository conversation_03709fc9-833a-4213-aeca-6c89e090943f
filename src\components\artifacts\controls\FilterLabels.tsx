import React, { useState } from 'react'
import { useAppStore } from '../../../store'

interface FilterLabel {
  name: string
  icon: string
  count: number
  styles: string
  type: string
}

export function FilterLabels() {
  const { artifacts: { artifacts } } = useAppStore()
  const [sortBy, setSortBy] = useState<'timestamp' | 'type'>('timestamp')

  // Count artifacts by type
  const getArtifactCounts = () => {
    const counts = {
      code: 0,
      document: 0,
      markdown: 0
    }

    artifacts.forEach(artifact => {
      switch (artifact.type) {
        case 'code':
        case 'json':
        case 'html':
          counts.code++
          break
        case 'markdown':
          counts.markdown++
          break
        default:
          counts.document++
          break
      }
    })

    return counts
  }

  const counts = getArtifactCounts()

  // Filter labels from JSON design specification
  const filterLabels: FilterLabel[] = [
    {
      name: "code",
      icon: "fa-solid fa-code",
      count: counts.code,
      styles: "bg-primary/20 text-primary border border-primary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1",
      type: "code"
    },
    {
      name: "document",
      icon: "fa-solid fa-file-text",
      count: counts.document,
      styles: "bg-supplement2/20 text-supplement2 border border-supplement2/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1",
      type: "document"
    },
    {
      name: "markdown",
      icon: "fa-brands fa-markdown",
      count: counts.markdown,
      styles: "bg-secondary/20 text-secondary border border-secondary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1",
      type: "markdown"
    }
  ]

  const handleSort = () => {
    setSortBy(sortBy === 'timestamp' ? 'type' : 'timestamp')
    // TODO: Implement actual sorting logic in the artifacts store
  }

  return (
    <>
      {/* Filter Labels Row */}
      <div className="px-4 py-2 border-b border-tertiary/50">
        <div className="flex gap-2">
          {filterLabels.map((label) => (
            <div key={label.name} className={label.styles}>
              <i className={`${label.icon} text-xs`}></i>
              <span className="capitalize">{label.name === 'document' ? 'Doc' : label.name === 'markdown' ? 'MD' : label.name}</span>
              <span className={`px-1 rounded ${
                label.name === 'code' ? 'bg-primary/30' :
                label.name === 'document' ? 'bg-supplement2/30' :
                'bg-secondary/30'
              }`}>
                {label.count}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Action Icons Row */}
      <div className="px-4 py-2 border-b border-tertiary/50">
        <div className="flex gap-1">
          {actionIcons.map((action) => (
            <button
              key={action.name}
              className="p-1.5 hover:bg-gray-700 rounded transition-colors group relative"
              title={action.tooltip}
            >
              <i className={`${action.icon} text-gray-400 text-xs`}></i>
              <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                {action.tooltip}
              </div>
            </button>
          ))}

          {/* Sort Button */}
          <button
            onClick={handleSort}
            className="p-1.5 hover:bg-gray-700 rounded transition-colors group relative ml-auto"
            title={`Sort by ${sortBy === 'timestamp' ? 'type' : 'timestamp'}`}
          >
            <i className="fa-solid fa-sort text-gray-400 text-xs"></i>
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Sort
            </div>
          </button>
        </div>
      </div>
    </>
  )
}

// Action Icons from JSON specification (for future use in document listing)
export const actionIcons = [
  {
    name: "render",
    icon: "fa-solid fa-eye",
    tooltip: "Render",
    styles: "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
  },
  {
    name: "source",
    icon: "fa-solid fa-code",
    tooltip: "Source",
    styles: "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
  },
  {
    name: "play",
    icon: "fa-solid fa-play",
    tooltip: "Play",
    styles: "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
  },
  {
    name: "copy",
    icon: "fa-solid fa-copy",
    tooltip: "Copy",
    styles: "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
  },
  {
    name: "html",
    icon: "fa-brands fa-html5",
    tooltip: "HTML",
    styles: "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
  },
  {
    name: "download",
    icon: "fa-solid fa-download",
    tooltip: "Download",
    styles: "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
  }
]
