# ChatLo Design System Index

## Overview
This document serves as the central index for the ChatLo Design System v1, replacing the previous ChatWise-inspired design with a cohesive, professional system based on the provided design reference.

## Key Files

### 1. Design System Documentation
- **`design_system_v1.html`** - Interactive component showcase and documentation
- **`chatlo_design_guidelines.md`** - Comprehensive design guidelines and usage instructions
- **`chatlo_design_reference.html`** - Original design reference provided by user
- **`design_system_index.md`** - This index file

### 2. Implementation Files
- **`tailwind.config.js`** - Updated with ChatLo color palette and design tokens
- **`src/index.css`** - Contains all u1-* component classes and legacy compatibility
- **`src/components/ChatLoLogo.tsx`** - Official ChatLo logo component

## Color System

### Primary Brand Colors
```css
primary: "#8AB0BB"      /* Teal - Main brand color */
secondary: "#FF8383"    /* Coral - Accent/heart color */
tertiary: "#1B3E68"     /* Navy - Deep accent */
supplement1: "#D5D8E0"  /* Light gray - Text/borders */
supplement2: "#89AFBA"  /* Muted teal - Secondary elements */
```

### Extended Palettes
- **chatlo-teal**: 50-900 scale based on primary color
- **chatlo-coral**: 50-900 scale based on secondary color  
- **chatlo-navy**: 50-900 scale based on tertiary color

## Component Naming System

All components use the `u1-` prefix (ChatLo Design System v1):

### Buttons
- `u1-button-primary` - Main action buttons
- `u1-button-secondary` - Secondary actions
- `u1-button-outline` - Outlined buttons
- `u1-button-icon` - Icon-only buttons
- `u1-button-ghost` - Minimal buttons
- `u1-button-nav` - Navigation buttons

### Inputs
- `u1-input-field` - Standard text input
- `u1-input-search` - Search input with icon
- `u1-textarea` - Multi-line text input
- `u1-chat-input` - Chat message input container

### Cards & Containers
- `u1-card` - Basic container
- `u1-chat-bubble-user` - User message bubble
- `u1-chat-bubble-assistant` - AI message bubble
- `u1-sidebar-item` - Sidebar list item
- `u1-status-card` - Status display card
- `u1-artifact-card` - Code/content artifact

### Navigation
- `u1-nav-iconbar` - VSCode-style icon bar
- `u1-nav-icon` - Individual nav icon
- `u1-nav-tabs` - Tab container
- `u1-tab-active` - Active tab
- `u1-tab-inactive` - Inactive tab

### Badges & Labels
- `u1-badge-primary` - Primary status
- `u1-badge-secondary` - Secondary status
- `u1-badge-success` - Success state
- `u1-badge-warning` - Warning state
- `u1-badge-error` - Error state

## Usage Examples

### Creating a Survey Button
```html
<button class="u1-button-primary">
  <i class="fa-solid fa-poll"></i>
  Create Survey
</button>
```

### Chat Input Component
```html
<div class="u1-chat-input">
  <button class="u1-button-ghost">
    <i class="fa-solid fa-paperclip"></i>
  </button>
  <textarea class="u1-textarea" placeholder="Type your message..."></textarea>
  <button class="u1-button-icon">
    <i class="fa-solid fa-paper-plane"></i>
  </button>
</div>
```

### Status Badge
```html
<div class="u1-badge-success">
  <i class="fa-solid fa-check"></i>
  Online
</div>
```

## Implementation Status

### ✅ Completed
- [x] Color system defined and implemented in Tailwind config
- [x] All u1-* component classes created in index.css
- [x] ChatLo logo component created
- [x] Design system documentation (HTML showcase)
- [x] Comprehensive design guidelines
- [x] Updated ChatArea component with new design
- [x] Updated Sidebar component with new design
- [x] Updated App.tsx with new color scheme
- [x] Backward compatibility maintained with legacy classes

### 🔄 In Progress
- [ ] Update remaining components to use new design system
- [ ] Update MessageBubble component
- [ ] Update InputArea component
- [ ] Update ModelSelector component
- [ ] Update Settings pages

### 📋 Future Enhancements
- [ ] Dark/Light theme toggle support
- [ ] Additional component variants
- [ ] Animation library integration
- [ ] Component composition patterns
- [ ] Performance optimizations

## Migration Guide

### For Developers
1. **New Components**: Use `u1-*` classes for all new components
2. **Existing Components**: Gradually migrate from legacy classes to u1-* classes
3. **Colors**: Use the new ChatLo color palette (primary, secondary, tertiary, etc.)
4. **Logo**: Use `<ChatLoLogo />` component instead of hardcoded logos

### Example Migration
```html
<!-- Old -->
<button class="btn-primary">Action</button>

<!-- New -->
<button class="u1-button-primary">Action</button>
```

### Color Migration
```html
<!-- Old -->
<div class="bg-indigo-500 text-white">Content</div>

<!-- New -->
<div class="bg-primary text-gray-900">Content</div>
```

## Design Principles

1. **Consistency** - Unified visual language across all components
2. **Clarity** - Clear hierarchy and readable typography
3. **Accessibility** - WCAG 2.1 AA compliant
4. **Modern Aesthetics** - Clean, professional interface

## Typography
- **Font**: Inter (Google Fonts)
- **Fallback**: system-ui, sans-serif
- **Scale**: text-xs to text-4xl with appropriate weights

## Animations
- **Standard**: `transition-colors` for hover states
- **Chat**: `animate-fade-in-up` for message bubbles
- **Loading**: `animate-pulse-slow` for loading states

## Browser Support
- Modern browsers with CSS Grid and Flexbox support
- Tailwind CSS compatibility
- Font Awesome 6.4.0 icons

## Maintenance Notes
- All design tokens are centralized in `tailwind.config.js`
- Component styles are in `src/index.css` under `@layer components`
- Logo variants available through `ChatLoLogo` component props
- Design system is versioned (v1) for future updates

## Contact & Updates
- Design system updates should be documented in this index
- New components should follow the u1-* naming convention
- Color changes should be made in `tailwind.config.js` first
- All changes should maintain backward compatibility where possible
