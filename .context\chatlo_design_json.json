{
  "theme": {
    "colors": {
      "primary": "#8AB0BB",
      "secondary": "#FF8383", 
      "tertiary": "#1B3E68",
      "supplement1": "#D5D8E0",
      "supplement2": "#89AFBA",
      "background": {
        "main": "#111827",
        "sidebar": "#1F2937",
        "chat": "#374151"
      },
      "text": {
        "primary": "#FFFFFF",
        "secondary": "#9CA3AF",
        "muted": "#6B7280"
      }
    },
    "fontFamily": {
      "sans": ["Inter", "sans-serif"]
    },
    "spacing": {
      "iconBar": "48px",
      "leftSidebar": "256px", 
      "rightSidebar": "320px"
    }
  },
  "layout": {
    "iconBar": {
      "width": "w-12",
      "background": "bg-gray-900",
      "border": "border-r border-tertiary",
      "padding": "py-2",
      "position": "left"
    },
    "leftSidebar": {
      "width": "w-64",
      "background": "bg-gray-800",
      "border": "border-r border-tertiary"
    },
    "mainChat": {
      "flex": "flex-1",
      "background": "bg-gray-900"
    },
    "rightSidebar": {
      "width": "w-80",
      "background": "bg-gray-800", 
      "border": "border-l border-tertiary"
    }
  },
  "icons": {
    "iconBar": {
      "navigation": [
        {
          "name": "home",
          "icon": "fa-solid fa-home",
          "tooltip": "Home",
          "active": false,
          "styles": "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
        },
        {
          "name": "chat",
          "icon": "fa-solid fa-comment",
          "tooltip": "Chat", 
          "active": true,
          "styles": "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-primary bg-primary/20 border-l-2 border-primary"
        },
        {
          "name": "history",
          "icon": "fa-solid fa-clock-rotate-left",
          "tooltip": "History",
          "active": false,
          "styles": "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
        },
        {
          "name": "files",
          "icon": "fa-solid fa-folder-tree",
          "tooltip": "Files",
          "active": false,
          "styles": "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
        }
      ],
      "bottom": [
        {
          "name": "profile",
          "icon": "fa-solid fa-user",
          "tooltip": "Profile",
          "styles": "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
        },
        {
          "name": "settings",
          "icon": "fa-solid fa-gear", 
          "tooltip": "Settings",
          "styles": "w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1"
        }
      ]
    },
    "leftSidebar": {
      "newChat": {
        "icon": "fa-solid fa-plus",
        "text": "New Chat",
        "styles": "w-full bg-primary hover:bg-primary/80 text-gray-900 font-medium py-3 px-4 rounded-lg flex items-center gap-3 transition-colors"
      },
      "statusIndicator": {
        "icon": "fa-solid fa-wifi",
        "text": "Online",
        "styles": "w-8 h-8 rounded-full bg-primary flex items-center justify-center text-gray-900 text-sm"
      },
      "privateMode": {
        "info": {
          "icon": "fa-solid fa-info-circle",
          "styles": "text-gray-400 text-xs hover:text-supplement1 transition-colors"
        }
      }
    },
    "chatHeader": {
      "avatar": {
        "icon": "fa-solid fa-robot",
        "styles": "w-10 h-10 bg-secondary rounded-full flex items-center justify-center text-white"
      },
      "menu": {
        "icon": "fa-solid fa-ellipsis-vertical",
        "styles": "p-2 hover:bg-gray-700 rounded-lg transition-colors text-gray-400"
      }
    },
    "chatMessages": {
      "aiAvatar": {
        "icon": "fa-solid fa-robot",
        "styles": "w-8 h-8 bg-secondary rounded-full flex items-center justify-center flex-shrink-0 text-white text-sm"
      },
      "userAvatar": {
        "type": "image",
        "src": "https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg",
        "styles": "w-8 h-8 rounded-full flex-shrink-0"
      }
    },
    "messageInput": {
      "attachment": {
        "icon": "fa-solid fa-paperclip",
        "styles": "p-2 text-gray-400 hover:text-supplement1 transition-colors"
      },
      "settings": {
        "icon": "fa-solid fa-sliders",
        "tooltip": "Chat Settings",
        "styles": "p-2 text-gray-400 hover:text-supplement1 transition-colors"
      },
      "send": {
        "icon": "fa-solid fa-paper-plane",
        "styles": "bg-primary hover:bg-primary/80 text-gray-900 w-10 h-10 rounded-xl transition-colors flex items-center justify-center"
      }
    },
    "rightSidebar": {
      "header": [
        {
          "name": "expand",
          "icon": "fa-solid fa-expand",
          "styles": "p-2 hover:bg-gray-700 rounded-lg transition-colors text-gray-400"
        },
        {
          "name": "close",
          "icon": "fa-solid fa-xmark",
          "styles": "p-2 hover:bg-gray-700 rounded-lg transition-colors text-gray-400"
        }
      ],
      "filterLabels": [
        {
          "name": "code",
          "icon": "fa-solid fa-code",
          "count": 3,
          "styles": "bg-primary/20 text-primary border border-primary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1"
        },
        {
          "name": "document",
          "icon": "fa-solid fa-file-text",
          "count": 2,
          "styles": "bg-supplement2/20 text-supplement2 border border-supplement2/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1"
        },
        {
          "name": "markdown",
          "icon": "fa-brands fa-markdown",
          "count": 1,
          "styles": "bg-secondary/20 text-secondary border border-secondary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1"
        }
      ],
      "actionIcons": [
        {
          "name": "render",
          "icon": "fa-solid fa-eye",
          "tooltip": "Render",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
        },
        {
          "name": "source",
          "icon": "fa-solid fa-code",
          "tooltip": "Source",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
        },
        {
          "name": "play",
          "icon": "fa-solid fa-play",
          "tooltip": "Play",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
        },
        {
          "name": "copy",
          "icon": "fa-solid fa-copy",
          "tooltip": "Copy",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
        },
        {
          "name": "html",
          "icon": "fa-brands fa-html5",
          "tooltip": "HTML",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
        },
        {
          "name": "download",
          "icon": "fa-solid fa-download",
          "tooltip": "Download",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
        },
        {
          "name": "sort",
          "icon": "fa-solid fa-sort",
          "tooltip": "Sort",
          "styles": "p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs ml-auto"
        }
      ],
      "artifactTypes": [
        {
          "name": "code",
          "icon": "fa-solid fa-code",
          "styles": "text-supplement2"
        },
        {
          "name": "image",
          "icon": "fa-solid fa-image",
          "styles": "text-supplement2"
        },
        {
          "name": "document",
          "icon": "fa-solid fa-file-text",
          "styles": "text-supplement2"
        }
      ]
    }
  },
  "components": {
    "tooltip": {
      "styles": "absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",
      "variants": {
        "bottom": "absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",
        "bottomWide": "absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap border border-tertiary shadow-lg w-64"
      }
    },
    "chatBubble": {
      "ai": {
        "container": "flex gap-3 chat-bubble",
        "bubble": "bg-gray-800 rounded-2xl rounded-tl-md p-4 max-w-lg",
        "text": "text-supplement1"
      },
      "user": {
        "container": "flex gap-3 justify-end chat-bubble",
        "bubble": "bg-primary rounded-2xl rounded-tr-md p-4 max-w-lg",
        "text": "text-gray-900"
      },
      "timestamp": "text-xs text-gray-400 mt-1 ml-1"
    },
    "privateToggle": {
      "container": "bg-gray-700/50 rounded-lg p-3",
      "switch": "relative inline-flex h-6 w-11 items-center rounded-full bg-secondary transition-colors",
      "switchHandle": "inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6",
      "statusText": "text-xs text-secondary font-medium"
    },
    "artifactCard": {
      "container": "bg-gray-700/50 rounded-lg p-4 mb-4",
      "header": "flex items-center gap-2 mb-3",
      "title": "font-medium text-supplement1",
      "codeBlock": "bg-gray-900 rounded p-3 text-sm font-mono"
    }
  },
  "animations": {
    "fadeInUp": {
      "keyframes": "fadeInUp 0.3s ease-out",
      "from": "opacity: 0, transform: translateY(10px)",
      "to": "opacity: 1, transform: translateY(0)"
    },
    "transitions": {
      "colors": "transition-colors",
      "opacity": "transition-opacity",
      "transform": "transition-transform"
    }
  },
  "external": {
    "fontAwesome": {
      "version": "6.4.0",
      "cdn": {
        "js": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js",
        "css": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css",
        "webfonts": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/",
        "svgs": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/"
      },
      "config": "window.FontAwesomeConfig = { autoReplaceSvg: 'nest'}",
      "iconPaths": {
        "fa-solid fa-home": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/house.svg",
        "fa-solid fa-comment": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/comment.svg",
        "fa-solid fa-clock-rotate-left": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/clock-rotate-left.svg",
        "fa-solid fa-folder-tree": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/folder-tree.svg",
        "fa-solid fa-user": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/user.svg",
        "fa-solid fa-gear": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/gear.svg",
        "fa-solid fa-plus": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/plus.svg",
        "fa-solid fa-wifi": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/wifi.svg",
        "fa-solid fa-info-circle": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/circle-info.svg",
        "fa-solid fa-robot": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/robot.svg",
        "fa-solid fa-ellipsis-vertical": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/ellipsis-vertical.svg",
        "fa-solid fa-paperclip": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/paperclip.svg",
        "fa-solid fa-sliders": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/sliders.svg",
        "fa-solid fa-paper-plane": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/paper-plane.svg",
        "fa-solid fa-expand": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/expand.svg",
        "fa-solid fa-xmark": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/xmark.svg",
        "fa-solid fa-code": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/code.svg",
        "fa-solid fa-file-text": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/file-lines.svg",
        "fa-brands fa-markdown": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/brands/markdown.svg",
        "fa-solid fa-eye": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/eye.svg",
        "fa-solid fa-play": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/play.svg",
        "fa-solid fa-copy": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/copy.svg",
        "fa-brands fa-html5": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/brands/html5.svg",
        "fa-solid fa-download": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/download.svg",
        "fa-solid fa-sort": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/sort.svg",
        "fa-solid fa-image": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/image.svg"
      }
    },
    "tailwind": {
      "cdn": "https://cdn.tailwindcss.com"
    },
    "fonts": {
      "inter": "https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap"
    }
  },
  "agentInstructions": {
    "iconCapture": {
      "methods": [
        {
          "name": "FontAwesome CDN SVGs",
          "description": "Direct access to individual SVG files",
          "baseUrl": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/",
          "example": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/house.svg",
          "note": "Replace 'house' with the actual icon name from FontAwesome"
        },
        {
          "name": "FontAwesome Web Fonts",
          "description": "Access to font files containing all icons",
          "baseUrl": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/",
          "files": [
            "fa-solid-900.woff2",
            "fa-brands-400.woff2",
            "fa-regular-400.woff2"
          ]
        },
        {
          "name": "Alternative Icon Libraries",
          "description": "If FontAwesome is not available, use these alternatives",
          "alternatives": [
            {
              "name": "Heroicons",
              "cdn": "https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/solid/",
              "format": "svg"
            },
            {
              "name": "Tabler Icons",
              "cdn": "https://cdn.jsdelivr.net/npm/@tabler/icons@2.40.0/icons/",
              "format": "svg"
            },
            {
              "name": "Lucide Icons",
              "cdn": "https://cdn.jsdelivr.net/npm/lucide@0.263.1/dist/esm/icons/",
              "format": "js"
            }
          ]
        }
      ],
      "iconMapping": {
        "note": "FontAwesome class names may differ from actual SVG filenames",
        "commonMappings": {
          "fa-solid fa-home": "house.svg",
          "fa-solid fa-comment": "comment.svg",
          "fa-solid fa-clock-rotate-left": "clock-rotate-left.svg",
          "fa-solid fa-folder-tree": "folder-tree.svg",
          "fa-solid fa-user": "user.svg",
          "fa-solid fa-gear": "gear.svg",
          "fa-solid fa-plus": "plus.svg",
          "fa-solid fa-wifi": "wifi.svg",
          "fa-solid fa-info-circle": "circle-info.svg",
          "fa-solid fa-robot": "robot.svg",
          "fa-solid fa-ellipsis-vertical": "ellipsis-vertical.svg",
          "fa-solid fa-paperclip": "paperclip.svg",
          "fa-solid fa-sliders": "sliders.svg",
          "fa-solid fa-paper-plane": "paper-plane.svg",
          "fa-solid fa-expand": "expand.svg",
          "fa-solid fa-xmark": "xmark.svg",
          "fa-solid fa-code": "code.svg",
          "fa-solid fa-file-text": "file-lines.svg",
          "fa-brands fa-markdown": "markdown.svg",
          "fa-solid fa-eye": "eye.svg",
          "fa-solid fa-play": "play.svg",
          "fa-solid fa-copy": "copy.svg",
          "fa-brands fa-html5": "html5.svg",
          "fa-solid fa-download": "download.svg",
          "fa-solid fa-sort": "sort.svg",
          "fa-solid fa-image": "image.svg"
        }
      }
    },
    "implementation": {
      "options": [
        {
          "name": "Use FontAwesome CDN",
          "description": "Include FontAwesome CSS/JS and use class names",
          "implementation": "<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">"
        },
        {
          "name": "Download SVGs",
          "description": "Download individual SVG files and embed them",
          "implementation": "wget https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/svgs/solid/house.svg"
        },
        {
          "name": "Use Alternative Libraries",
          "description": "Replace with Heroicons or Tabler icons that have similar designs",
          "implementation": "Map FontAwesome icons to similar icons from other libraries"
        }
      ]
    }
  }
    "branding": {
      "appName": "Dark Chat",
      "aiAssistant": "AI Assistant",
      "status": "Ready to help"
    },
    "navigation": {
      "recentChats": "Recent Chats",
      "artifacts": "Artifacts",
      "privateMode": "Private Mode",
      "privateModeTooltip": "Local models only. All documents are not sharing with large language models"
    },
    "inputPlaceholder": "Type your message...",
    "modelInfo": {
      "model": "DeepSeek V3 Base (free)",
      "temperature": "0.1",
      "maxTokens": "4,096",
      "topP": "0.90",
      "hint": "Shift+Enter for new line"
    },
    "sampleMessages": {
      "aiWelcome": "Hello! I'm your AI assistant. How can I help you today?",
      "userQuery": "I need help with creating a modern dashboard design. Can you provide some guidance?",
      "aiResponse": "Absolutely! I'd be happy to help you create a modern dashboard design. Here are some key principles to consider:",
      "designPrinciples": [
        "Clean, minimalist layout",
        "Consistent color scheme", 
        "Clear data visualization",
        "Responsive design"
      ]
    }
  }
}