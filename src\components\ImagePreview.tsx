import React, { useState, useEffect } from 'react'
import { FileRecord } from '../types'
import { X, Eye, Download, RotateCcw, Crop } from './Icons'
import ImageEditor from './ImageEditor'

interface ImagePreviewProps {
  file: FileRecord
  onRemove?: () => void
  onFileUpdate?: (newFile: FileRecord) => void
  className?: string
}

const ImagePreview: React.FC<ImagePreviewProps> = ({ file, onRemove, onFileUpdate, className = '' }) => {
  const [imageData, setImageData] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showFullSize, setShowFullSize] = useState(false)
  const [showEditor, setShowEditor] = useState(false)

  useEffect(() => {
    loadImageData()
  }, [file.filepath])

  const loadImageData = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      if (window.electronAPI?.files) {
        // Get file content as base64
        const content = await window.electronAPI.files.getFileContent(file.filepath)
        if (content) {
          // Create data URL for image display
          const mimeType = file.mime_type || 'image/png'
          const dataUrl = `data:${mimeType};base64,${content}`
          setImageData(dataUrl)
        } else {
          setError('Failed to load image content')
        }
      } else {
        setError('File system not available')
      }
    } catch (err) {
      console.error('Error loading image:', err)
      setError('Failed to load image')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const handleDownload = async () => {
    try {
      if (window.electronAPI?.files && imageData) {
        // Trigger download
        const link = document.createElement('a')
        link.href = imageData
        link.download = file.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  const handleEditSave = async (editedBlob: Blob, filename: string) => {
    try {
      if (window.electronAPI?.files) {
        // Convert blob to base64
        const arrayBuffer = await editedBlob.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)
        let binary = ''
        const chunkSize = 8192
        for (let i = 0; i < uint8Array.length; i += chunkSize) {
          const chunk = uint8Array.slice(i, i + chunkSize)
          binary += String.fromCharCode(...chunk)
        }
        const base64String = btoa(binary)

        // Save the edited file
        const fileId = await window.electronAPI.files.saveFile(filename, base64String)

        if (fileId && onFileUpdate) {
          // Get the new file record
          const files = await window.electronAPI.files.getIndexedFiles()
          const newFile = files.find(f => f.id === fileId)
          if (newFile) {
            onFileUpdate(newFile)
          }
        }
      }
      setShowEditor(false)
    } catch (error) {
      console.error('Error saving edited image:', error)
    }
  }

  if (isLoading) {
    return (
      <div className={`relative bg-neutral-800 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
        </div>
      </div>
    )
  }

  if (error || !imageData) {
    return (
      <div className={`relative bg-neutral-800 rounded-lg p-4 border border-neutral-700 ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-red-500/20 rounded flex items-center justify-center">
              <X className="w-4 h-4 text-red-400" />
            </div>
            <div>
              <div className="text-sm font-medium text-white truncate">{file.filename}</div>
              <div className="text-xs text-red-400">{error}</div>
            </div>
          </div>
          {onRemove && (
            <button
              onClick={onRemove}
              className="p-1 hover:bg-neutral-700 rounded transition-colors"
              title="Remove image"
            >
              <X className="w-4 h-4 text-neutral-400 hover:text-red-400" />
            </button>
          )}
        </div>
        <button
          onClick={loadImageData}
          className="flex items-center gap-2 text-sm text-indigo-400 hover:text-indigo-300 transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          Retry
        </button>
      </div>
    )
  }

  return (
    <>
      <div className={`relative bg-neutral-800 rounded-lg overflow-hidden border border-neutral-700 ${className}`}>
        {/* Image Header */}
        <div className="flex items-center justify-between p-3 bg-neutral-900/50">
          <div className="flex items-center gap-2 min-w-0">
            <div className="text-sm font-medium text-white truncate">{file.filename}</div>
            <div className="text-xs text-neutral-400">
              {formatFileSize(file.file_size)}
            </div>
            {file.extracted_content && (
              <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">
                ✓ Processed
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowFullSize(true)}
              className="p-1 hover:bg-neutral-700 rounded transition-colors"
              title="View full size"
            >
              <Eye className="w-4 h-4 text-neutral-400 hover:text-indigo-400" />
            </button>
            <button
              onClick={() => setShowEditor(true)}
              className="p-1 hover:bg-neutral-700 rounded transition-colors"
              title="Edit image"
            >
              <Crop className="w-4 h-4 text-neutral-400 hover:text-green-400" />
            </button>
            <button
              onClick={handleDownload}
              className="p-1 hover:bg-neutral-700 rounded transition-colors"
              title="Download image"
            >
              <Download className="w-4 h-4 text-neutral-400 hover:text-indigo-400" />
            </button>
            {onRemove && (
              <button
                onClick={onRemove}
                className="p-1 hover:bg-neutral-700 rounded transition-colors"
                title="Remove image"
              >
                <X className="w-4 h-4 text-neutral-400 hover:text-red-400" />
              </button>
            )}
          </div>
        </div>

        {/* Image Preview */}
        <div className="relative">
          <img
            src={imageData}
            alt={file.filename}
            className="w-full h-48 object-cover cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => setShowFullSize(true)}
            onError={() => setError('Failed to display image')}
          />
          
          {/* Overlay with image info */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
            <div className="text-xs text-white/80">
              Click to view full size
            </div>
          </div>
        </div>
      </div>

      {/* Full Size Modal */}
      {showFullSize && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-full max-h-full">
            <button
              onClick={() => setShowFullSize(false)}
              className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-full transition-colors z-10"
            >
              <X className="w-6 h-6 text-white" />
            </button>
            <img
              src={imageData}
              alt={file.filename}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <div className="absolute bottom-4 left-4 right-4 bg-black/50 rounded-lg p-3">
              <div className="text-white font-medium">{file.filename}</div>
              <div className="text-white/70 text-sm">{formatFileSize(file.file_size)}</div>
            </div>
          </div>
        </div>
      )}

      {/* Image Editor */}
      {showEditor && imageData && (
        <ImageEditor
          imageUrl={imageData}
          filename={file.filename}
          onSave={handleEditSave}
          onClose={() => setShowEditor(false)}
        />
      )}
    </>
  )
}

export default ImagePreview
